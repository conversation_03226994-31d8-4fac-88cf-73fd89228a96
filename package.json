{"name": "nuxt-ui-template-changelog", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@iconify-json/lucide": "^1.2.68", "@iconify-json/simple-icons": "^1.2.53", "@nuxt/ui": "^4.0.0", "@nuxtjs/mdc": "0.17.4", "@supabase/supabase-js": "^2.58.0", "nuxt": "^4.1.2", "remark-github": "^12.0.0"}, "devDependencies": {"@nuxt/eslint": "^1.9.0", "@types/node": "^24.5.2", "eslint": "^9.36.0", "nuxt-lodash": "^2.5.3", "typescript": "^5.9.2", "vue-tsc": "^3.0.8"}, "resolutions": {"unimport": "4.1.1"}, "packageManager": "pnpm@10.17.1"}