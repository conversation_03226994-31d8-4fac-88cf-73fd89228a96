<script setup>
useHead({
  meta: [{ name: 'viewport', content: 'width=device-width, initial-scale=1' }],
  link: [{ rel: 'icon', href: '/favicon.ico' }],
  htmlAttrs: {
    lang: 'en'
  }
})

const title = 'Nuxt Changelog Template'
const description
  = 'Display GitHub release notes as a beautiful changelog for any repository with this Nuxt UI template.'

useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description,
  ogImage: 'https://ui.nuxt.com/assets/templates/nuxt/changelog-light.png',
  twitterImage: 'https://ui.nuxt.com/assets/templates/nuxt/changelog-light.png',
  twitterCard: 'summary_large_image'
})

const links = ref([
  {
    label: '営業時間: 09:00 - 18:00',
    icon: 'streamline-ultimate:shop-sign-open-bold',
    to: 'https://github.com/nuxt/ui/releases'
  },
  {
    label: '電話番号：080-8806-1557',
    icon: 'line-md:phone-call-filled',
    to: 'tel:+8108088061557',
    target: '_blank'
  },
  {
    label: '〒133-0061 東京都江戸川区篠崎町2丁目8-4',
    icon: 'line-md:map-marker-alt-filled',
    to: 'https://maps.app.goo.gl/BJzBagfvJJ3JdzuJ6',
    target: '_blank'
  }

])
</script>

<template>
  <UApp>
    <div class="min-h-screen xl:grid xl:grid-cols-3">
      <UPageSection
        class="w-full"
        orientation="vertical"
        :links="[
          {
            label: '電話をかける',
            icon: 'line-md:phone-call-filled',
            size: 'md',
            to: 'tel:+8108088061557',
            target: '_blank',
            class: 'w-full'
          },
          {
            label: '店をみる',
            icon: 'line-md:map-marker-alt-filled',
            variant: 'outline',
            size: 'md',
            to: 'https://github.com/nuxt-ui-templates/changelog',
            target: '_blank',
            class: 'w-full'
          }
        ]"
        :ui="{
          root: 'border-b border-default xl:border-b-0 xl:sticky xl:inset-y-0 xl:h-screen overflow-hidden',
          container: 'h-full items-center justify-center',
          wrapper: 'flex flex-col',
          headline: 'mb-1',
          title: 'text-left text-4xl',
          description: 'text-left max-w-lg',
          links: 'gap-4 justify-start -ms-2.5 w-full grid grid-cols-2'
        }"
      >
        <template #top>
          <SkyBg />

          <div
            class="absolute -right-1/2 z-[-1] rounded-full bg-primary blur-[300px] size-60 sm:size-100 transform -translate-y-1/2 top-1/2"
          />
        </template>

        <template #headline>
          <div>住宅街に佇む隠れ家的お店</div>
        </template>
        <template #description>
          <div class="text-xl font-bold">
            旬のお野菜をふんだんに<br>
            ココロとカラダにやさしい食事
          </div>
          <div class="mt-4">
            <UPageLinks :links="links" />
          </div>
        </template>
        <template #title>
          <AppLogo class="w-full" />
        </template>
        <!-- <img
          src="/assets/images/dung_bg.png"
          class="w-full object-cover"
        > -->
      </UPageSection>

      <section class="px-4 sm:px-6 xl:px-0 xl:flex-1 xl:col-span-2">
        <UColorModeButton class="fixed top-4 right-4 z-10" />

        <NuxtPage />
      </section>
    </div>
  </UApp>
</template>
