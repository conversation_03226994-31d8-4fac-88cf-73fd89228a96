// Examples of different ways to import and use your global types

// Method 1: Using the root alias (~~ points to project root)
import type { Product } from '~~/types'

// Method 2: Using relative path
// import type { Product } from '../../types'

// Method 3: Using the @ alias (points to app directory, so we need to go up)
// import type { Product } from '@/../types'

// Example usage of the Product type
export const createSampleProduct = (): Product => {
  return {
    id: '1',
    created_at: new Date().toISOString(),
    name: 'Sample Product',
    category: 'Electronics',
    base_price: 99.99,
    unit: 'piece',
    tags: ['sample', 'electronics'],
    description: 'This is a sample product',
    slug: 'sample-product',
    category_detail: 'Consumer Electronics'
  }
}

// Example function that works with Product arrays
export const filterProductsByCategory = (products: Product[], category: string): Product[] => {
  return products.filter(product => product.category === category)
}

// Example function that creates a product with partial data
export const createProductFromPartial = (partial: Partial<Product>): Product => {
  const defaults: Product = {
    id: '',
    created_at: new Date().toISOString(),
    name: '',
    category: '',
    base_price: 0,
    unit: 'piece',
    tags: [],
    description: null,
    slug: '',
    category_detail: null
  }

  return { ...defaults, ...partial }
}

// Example of using Product type in a class
export class ProductManager {
  private products: Product[] = []

  addProduct(product: Product): void {
    this.products.push(product)
  }

  getProduct(id: string): Product | undefined {
    return this.products.find(p => p.id === id)
  }

  updateProduct(id: string, updates: Partial<Product>): boolean {
    const index = this.products.findIndex(p => p.id === id)
    if (index !== -1) {
      this.products[index] = { ...this.products[index], ...updates } as Product
      return true
    }
    return false
  }

  getAllProducts(): readonly Product[] {
    return this.products
  }
}

// Example of type guards
export const isValidProduct = (obj: any): obj is Product => {
  return (
    typeof obj === 'object'
    && obj !== null
    && typeof obj.id === 'string'
    && typeof obj.name === 'string'
    && typeof obj.category === 'string'
    && typeof obj.base_price === 'number'
    && typeof obj.unit === 'string'
    && Array.isArray(obj.tags)
    && typeof obj.slug === 'string'
  )
}
