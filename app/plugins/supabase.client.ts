import { createClient, type SupabaseClient } from '@supabase/supabase-js'

export default defineNuxtPlugin(() => {
  const runtimeConfig = useRuntimeConfig()
  const supabaseUrl = runtimeConfig.public.supabase.url
  const supabaseKey = runtimeConfig.public.supabase.key
  console.log('🚀 ~ supabaseKey:', supabaseKey)

  const supabase = createClient(supabaseUrl, supabaseKey)

  return {
    provide: {
      supabase: supabase as SupabaseClient
    }
  }
})
