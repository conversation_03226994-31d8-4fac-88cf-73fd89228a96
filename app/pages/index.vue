<script setup lang="ts">
const appConfig = useAppConfig()

const { productsTransformed, fetchProducts } = useProducts()

await fetchProducts()

const cards = ref([
  {
    title: 'トマト',
    description: '300円/3個',
    icon: 'healthicons:vegetables',
    class: 'lg:col-span-1',
    image: {
      path: '/images/items/tomato.png'
    },
    orientation: 'vertical' as const
  },

  {
    title: 'りんご',
    description: '300円/3個',
    icon: 'healthicons:vegetables',
    class: 'lg:col-span-1',
    image: {
      path: '/images/items/apples.png'
    },
    orientation: 'vertical' as const
  }, {
    title: '人参',
    description: '400円/3個',
    icon: 'healthicons:vegetables',
    class: 'lg:col-span-1',
    image: {
      path: '/images/items/carrots.png'
    },
    orientation: 'vertical' as const
  },
  {
    title: 'Color Mode',
    description: 'Nuxt UI integrates with Nuxt Color Mode to switch between light and dark.',
    icon: 'i-lucide-sun-moon',
    variant: 'soft' as const
  },
  {
    title: 'Icons',
    description: 'Nuxt UI integrates with Nuxt Icon to access over 200,000+ icons from Iconify.',
    icon: 'i-lucide-smile',
    image: {
      path: 'https://ui2.nuxt.com/illustrations/icon-library',
      width: 362,
      height: 184
    },
    class: 'lg:col-span-2',
    orientation: 'horizontal' as const,
    reverse: true
  }
])
</script>

<template>
  <UPage class="p-6">
    <UPageGrid>
      <ProductCard
        v-for="(card, index) in productsTransformed"
        :key="index"
        :product="card"
      >
        <!-- <UColorModeImage
          v-if="card.image"
          :light="`${card.image.path}`"
          :dark="`${card.image.path}`"
          :width="card.image.width"
          :height="card.image.height"
          :alt="card.title"
          loading="lazy"
          class="w-full"
        /> -->
      </ProductCard>
    </UPageGrid>
  </UPage>
</template>
