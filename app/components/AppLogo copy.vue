<template>
  <div class="logo-container">
    <!-- Main Logo -->
    <div class="logo-wrapper">
      <!-- Store Name -->
      <div class="store-name">
        <div class="veggie-letters">
          <span class="letter letter-y">Y</span>
          <span class="letter letter-a">a</span>
          <span class="letter letter-space">&nbsp;</span>
          <span class="letter letter-o">O</span>
          <span class="letter letter-space">&nbsp;</span>
          <span class="letter letter-y2">Y</span>
          <span class="letter letter-a2">a</span>
          <span class="letter letter-space">&nbsp;</span>
          <span class="letter letter-t">T</span>
          <span class="letter letter-o2">o</span>
        </div>
        <p class="subtitle">
          住宅街に佇む隠れ家的お店
        </p>
        <div />
      </div>
    </div>
  </div>
</template>

<style scoped>
.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff0 50%, #f5fffa 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(46, 125, 50, 0.15);
  max-width: 400px;
  margin: 0 auto;
}

.logo-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  animation: gentleBounce 3s ease-in-out infinite;
}

.produce-icons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.fruit-group, .veggie-group {
  display: flex;
  gap: 0.5rem;
  font-size: 1.5rem;
  animation: float 4s ease-in-out infinite;
}

.fruit-group {
  animation-delay: -0.5s;
}

.veggie-group {
  animation-delay: -2s;
}

.store-name {
  margin-bottom: 0.75rem;
}

.veggie-letters {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.letter {
  font-size: 2.5rem;
  font-weight: bold;
  font-family: 'Georgia', serif;
  display: inline-block;
  transition: all 0.3s ease;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.letter:hover {
  transform: scale(1.2) rotate(5deg);
}

/* Y - Carrot style (Cà rốt) */
.letter-y {
  background: linear-gradient(45deg, #ff8f00, #ff6f00, #e65100);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: carrotBounce 2s ease-in-out infinite;
}

/* a - Apple style (Táo) */
.letter-a {
  background: linear-gradient(45deg, #d32f2f, #f44336, #ff5722);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: appleSway 2.5s ease-in-out infinite;
}

/* O - Orange style (Cam) */
.letter-o {
  background: linear-gradient(45deg, #ff9800, #ffc107, #ffeb3b);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: orangeRoll 3s ease-in-out infinite;
  border-radius: 50%;
}

/* Y2 - Broccoli style (Súp lơ xanh) */
.letter-y2 {
  background: linear-gradient(45deg, #2e7d32, #4caf50, #66bb6a);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: broccoliGrow 2.2s ease-in-out infinite;
}

/* a2 - Avocado style (Bơ) */
.letter-a2 {
  background: linear-gradient(45deg, #388e3c, #4caf50, #8bc34a);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: avocadoSwing 2.8s ease-in-out infinite;
}

/* T - Tomato style (Cà chua) */
.letter-t {
  background: linear-gradient(45deg, #c62828, #e53935, #f44336);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: tomatoBounce 2.3s ease-in-out infinite;
}

/* o2 - Onion style (Hành tây) */
.letter-o2 {
  background: linear-gradient(45deg, #8e24aa, #ab47bc, #ba68c8);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: onionWave 2.7s ease-in-out infinite;
}

.letter-space {
  width: 0.5rem;
}

/* Vegetable-inspired animations */
@keyframes carrotBounce {
  0%, 100% { transform: translateY(0px) scaleY(1); }
  50% { transform: translateY(-8px) scaleY(1.1); }
}

@keyframes appleSway {
  0%, 100% { transform: rotate(0deg) scale(1); }
  25% { transform: rotate(-3deg) scale(1.05); }
  75% { transform: rotate(3deg) scale(1.05); }
}

@keyframes orangeRoll {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes broccoliGrow {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1) translateY(-3px); }
}

@keyframes avocadoSwing {
  0%, 100% { transform: rotateZ(0deg); }
  25% { transform: rotateZ(-5deg) translateY(-2px); }
  75% { transform: rotateZ(5deg) translateY(-2px); }
}

@keyframes tomatoBounce {
  0%, 100% { transform: translateY(0px); }
  30% { transform: translateY(-5px) scaleX(1.05); }
  60% { transform: translateY(-2px) scaleX(1.02); }
}

@keyframes onionWave {
  0%, 100% { transform: scaleX(1) scaleY(1); }
  50% { transform: scaleX(1.1) scaleY(0.9) translateY(-3px); }
}

.subtitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: #16a34a;
  opacity: 0.8;
  letter-spacing: 1px;
  font-family: 'Arial', sans-serif;
}

.decorative-leaves {
  display: flex;
  gap: 0.5rem;
  font-size: 1.2rem;
  opacity: 0.7;
  animation: sway 3s ease-in-out infinite alternate;
}

.cart-icon {
  margin-left: 0.75rem;
  font-size: 1.5rem;
  animation: wiggle 2s ease-in-out infinite;
  color: #ff6b35;
}

/* Animations */
@keyframes gentleBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-5px) rotate(2deg);
  }
}

@keyframes sway {
  0% {
    transform: rotate(-2deg);
  }
  100% {
    transform: rotate(2deg);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .logo-container {
    padding: 1rem;
    max-width: 320px;
  }

  .letter {
    font-size: 2rem;
  }

  .veggie-letters {
    gap: 0.1rem;
  }

  .produce-icons {
    gap: 0.5rem;
  }

  .fruit-group, .veggie-group {
    font-size: 1.2rem;
  }
}

/* Hover Effects */
.logo-container:hover .produce-icons {
  animation-duration: 2s;
}

.logo-container:hover .cart-icon {
  animation-duration: 1s;
}

.logo-container:hover .main-title {
  background: linear-gradient(45deg, #1b5e20, #2e7d32, #4caf50, #66bb6a);
  background-clip: text;
  -webkit-background-clip: text;
}
</style>
