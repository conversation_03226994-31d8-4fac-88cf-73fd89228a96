<template>
  <div class="pt-10 group">
    <UCard
      class="product-card overflow-visible hover:ring-primary hover:shadow-2xl"
    >
      <div class="relative xl:h-32 bg-gray-100 dark:bg-gray-800 rounded-2xl">
        <img
          :src="product.image.path"
          :alt="product.name"
          class="w-full xl:absolute inset-0 object-cover xl:-top-20 group-hover:scale-3d group-hover:scale-110 transition-all duration-200"
        >
      </div>
      <div class="space-y-2 mt-2">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">
            {{ product.name }}
          </h3>
          <UBadge
            :label="product.category"
            color="primary"
          />
        </div>
        <p
          v-if="product.description"
          class="text-gray-600"
        >
          {{ product.description }}
        </p>

        <div
          v-if="product.tags.length > 0"
          class="flex flex-wrap gap-1"
        >
          <UBadge
            :label="product.category_detail || ''"
            variant="soft"
          />
          <UBadge
            v-for="tag in product.tags"
            :key="tag"
            :label="tag"
            variant="soft"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-between">
          <span class="text-xl font-bold">{{ product.base_price }}</span>
          <span class="text-md text-gray-500">{{ product.unit }}</span>
        </div>
      </template>
    </UCard>
  </div>
</template>

<script setup lang="ts">
// Import the Product type using the global alias
import type { Product } from '~~/types'

// Define props with proper typing
interface Props {
  product: Product
}

// Define emits with proper typing
interface Emits {
  edit: [product: Product]
  delete: [id: string]
}

// Use the props and emits
defineProps<Props>()
defineEmits<Emits>()
</script>
