import type { Product } from '~~/types'

export const useProducts = () => {
  const products = ref<Product[]>([])

  const fetchProducts = async () => {
    try {
      const nuxtApp = useNuxtApp()
      const { data, error } = await nuxtApp.$supabase.from('products').select()
      if (error) throw error
      products.value = data || []
    } catch (error) {
      console.error('Error fetching products:', error)
    }
  }

  const productsTransformed = computed(() => {
    return products.value.map((product) => {
      return {
        ...product,
        base_price: product.base_price.toLocaleString('ja-JP', { style: 'currency', currency: 'JPY' }),
        image: {
          path: `/images/items/${product.slug}.png`
        },
        title: product.name
      }
    })
  })

  return {
    products,
    fetchProducts,
    productsTransformed
  }
}
